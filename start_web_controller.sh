#!/bin/bash

echo "🚀 AI Gesture Controller Web Server"
echo "===================================="
echo ""
echo "Starting the revolutionary web-based gesture controller..."
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python is not installed"
        echo "Please install Python 3.9+ from https://python.org"
        echo ""
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Check Python version
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | grep -oE '[0-9]+\.[0-9]+')
MAJOR_VERSION=$(echo $PYTHON_VERSION | cut -d. -f1)
MINOR_VERSION=$(echo $PYTHON_VERSION | cut -d. -f2)

if [ "$MAJOR_VERSION" -lt 3 ] || ([ "$MAJOR_VERSION" -eq 3 ] && [ "$MINOR_VERSION" -lt 9 ]); then
    echo "⚠️  Python $PYTHON_VERSION detected"
    echo "Python 3.9+ recommended for best performance"
    echo ""
fi

# Check if required files exist
if [ ! -f "index.html" ]; then
    echo "❌ index.html not found"
    echo "Please make sure all files are in the same directory"
    exit 1
fi

if [ ! -f "gesture-controller.js" ]; then
    echo "❌ gesture-controller.js not found"
    echo "Please make sure all files are in the same directory"
    exit 1
fi

echo "✅ All files found"
echo "🌐 Starting web server..."
echo ""

# Make the script executable if it isn't already
chmod +x "$0"

# Start the web server
$PYTHON_CMD web_server.py

echo ""
echo "🛑 Server stopped"
