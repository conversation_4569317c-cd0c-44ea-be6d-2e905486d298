<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 AI Gesture Gaming Controller</title>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/control_utils/control_utils.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils/drawing_utils.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/hands/hands.js" crossorigin="anonymous"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .video-section {
            flex: 2;
            min-width: 600px;
        }

        .video-container {
            position: relative;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        #videoElement {
            width: 100%;
            height: auto;
            display: block;
        }

        #canvasElement {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .controls {
            padding: 15px;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn.active {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
        }

        .sidebar {
            flex: 1;
            min-width: 300px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .panel h3 {
            margin-bottom: 15px;
            font-size: 1.3em;
            color: #ffd700;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .gesture-display {
            text-align: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .current-gesture {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .confidence-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ffd700, #4ecdc4);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .stat-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #4ecdc4;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .gesture-guide {
            max-height: 300px;
            overflow-y: auto;
        }

        .gesture-item {
            display: flex;
            align-items: center;
            padding: 8px;
            margin-bottom: 5px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .gesture-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .gesture-emoji {
            font-size: 1.5em;
            margin-right: 10px;
        }

        .gesture-info {
            flex: 1;
        }

        .gesture-name {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .gesture-action {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .game-profiles {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .profile-btn {
            flex: 1;
            padding: 8px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .profile-btn.active {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 25px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            font-weight: bold;
            z-index: 1000;
        }

        .status-indicator.active {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }

        .network-status {
            position: fixed;
            top: 70px;
            right: 20px;
            padding: 8px 12px;
            border-radius: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            font-size: 0.9em;
            z-index: 1000;
        }

        .network-status.status-ok {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }

        .network-status.status-warning {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .game-connection {
            position: fixed;
            top: 120px;
            right: 20px;
            padding: 8px 12px;
            border-radius: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            font-size: 0.9em;
            z-index: 1000;
        }

        .offline-notice {
            position: fixed;
            bottom: 20px;
            right: 20px;
            max-width: 300px;
            padding: 15px;
            background: rgba(255, 165, 0, 0.9);
            border-radius: 10px;
            color: white;
            z-index: 1001;
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2em;
        }

        .error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid rgba(255, 0, 0, 0.5);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .video-section {
                min-width: auto;
            }
            
            .sidebar {
                min-width: auto;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .glow {
            box-shadow: 0 0 20px rgba(78, 205, 196, 0.5);
        }
    </style>
</head>
<body>
    <div class="status-indicator" id="statusIndicator">
        🔴 Initializing...
    </div>

    <div class="network-status" id="networkStatus">
        🔍 Checking network...
    </div>

    <div class="game-connection" id="gameConnection">
        🎮 Detecting games...
    </div>

    <div class="header">
        <h1>🚀 AI Gesture Gaming Controller</h1>
        <p>Revolutionary browser-based gesture recognition with real-time AI analysis</p>
    </div>

    <div class="container">
        <div class="video-section">
            <div class="video-container">
                <video id="videoElement" autoplay muted playsinline></video>
                <canvas id="canvasElement"></canvas>
            </div>
            <div class="controls">
                <button class="btn" id="startBtn">🎥 Start Camera</button>
                <button class="btn" id="stopBtn">⏹️ Stop</button>
                <button class="btn" id="resetBtn">🔄 Reset Stats</button>
                <button class="btn" id="fullscreenBtn">🖥️ Fullscreen</button>
            </div>
        </div>

        <div class="sidebar">
            <div class="panel">
                <h3>🎯 Current Gesture</h3>
                <div class="gesture-display">
                    <div class="current-gesture" id="currentGesture">👋 Show your hand</div>
                    <div class="confidence-bar">
                        <div class="confidence-fill" id="confidenceFill" style="width: 0%"></div>
                    </div>
                    <div id="confidenceText">Confidence: 0%</div>
                </div>
            </div>

            <div class="panel">
                <h3>📊 Performance Stats</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="gestureCount">0</div>
                        <div class="stat-label">Gestures</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="accuracy">0%</div>
                        <div class="stat-label">Accuracy</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="bestStreak">0</div>
                        <div class="stat-label">Best Streak</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="sessionTime">0s</div>
                        <div class="stat-label">Session</div>
                    </div>
                </div>
            </div>

            <div class="panel">
                <h3>🎮 Game Profiles</h3>
                <div class="game-profiles">
                    <button class="profile-btn active" data-profile="temple_run">Temple Run</button>
                    <button class="profile-btn" data-profile="subway_surfers">Subway</button>
                    <button class="profile-btn" data-profile="custom">Custom</button>
                </div>
            </div>

            <div class="panel">
                <h3>✋ Gesture Guide</h3>
                <div class="gesture-guide" id="gestureGuide">
                    <!-- Gesture items will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script src="gesture-controller.js"></script>
</body>
</html>
