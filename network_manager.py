#!/usr/bin/env python3
"""
🌐 Network Manager - Solve Network Issues & Connection Problems
Comprehensive network troubleshooting and connection management
"""

import socket
import subprocess
import platform
import time
import threading
import json
import urllib.request
import urllib.error
from typing import Dict, List, Tuple, Optional

class NetworkManager:
    """Comprehensive network management and troubleshooting"""
    
    def __init__(self):
        self.system = platform.system()
        self.local_ip = self.get_local_ip()
        self.available_ports = []
        self.network_status = {}
        
        print("🌐 Network Manager initialized")
        print(f"💻 System: {self.system}")
        print(f"🔗 Local IP: {self.local_ip}")

    def get_local_ip(self) -> str:
        """Get local IP address"""
        try:
            # Connect to a remote address to determine local IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
            return local_ip
        except Exception:
            return "127.0.0.1"

    def check_internet_connection(self) -> bool:
        """Check if internet connection is available"""
        test_urls = [
            "https://www.google.com",
            "https://www.cloudflare.com",
            "https://*******"
        ]
        
        for url in test_urls:
            try:
                urllib.request.urlopen(url, timeout=5)
                print("✅ Internet connection: Available")
                return True
            except urllib.error.URLError:
                continue
        
        print("❌ Internet connection: Not available")
        return False

    def find_available_ports(self, start_port: int = 8000, count: int = 10) -> List[int]:
        """Find available ports for servers"""
        available_ports = []
        
        for port in range(start_port, start_port + count * 10):
            if self.is_port_available(port):
                available_ports.append(port)
                if len(available_ports) >= count:
                    break
        
        self.available_ports = available_ports
        print(f"🔌 Available ports: {available_ports[:5]}...")
        return available_ports

    def is_port_available(self, port: int) -> bool:
        """Check if a port is available"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False

    def test_port_connectivity(self, port: int, host: str = 'localhost') -> bool:
        """Test if a port is accessible"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(2)
                result = s.connect_ex((host, port))
                return result == 0
        except Exception:
            return False

    def diagnose_network_issues(self) -> Dict[str, any]:
        """Comprehensive network diagnostics"""
        print("🔍 Running network diagnostics...")
        
        diagnostics = {
            'internet_connection': self.check_internet_connection(),
            'local_ip': self.local_ip,
            'available_ports': self.find_available_ports(),
            'firewall_status': self.check_firewall_status(),
            'dns_resolution': self.test_dns_resolution(),
            'localhost_connectivity': self.test_localhost_connectivity(),
            'common_ports_status': self.check_common_ports(),
            'network_interfaces': self.get_network_interfaces()
        }
        
        return diagnostics

    def check_firewall_status(self) -> Dict[str, str]:
        """Check firewall status"""
        firewall_info = {'status': 'unknown', 'details': ''}
        
        try:
            if self.system == 'Windows':
                result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    firewall_info['status'] = 'active' if 'ON' in result.stdout else 'inactive'
                    firewall_info['details'] = result.stdout
            
            elif self.system == 'Linux':
                # Check ufw
                result = subprocess.run(['ufw', 'status'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    firewall_info['status'] = 'active' if 'active' in result.stdout.lower() else 'inactive'
                    firewall_info['details'] = result.stdout
            
            elif self.system == 'Darwin':  # macOS
                result = subprocess.run(['pfctl', '-s', 'info'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    firewall_info['details'] = result.stdout
                    
        except Exception as e:
            firewall_info['details'] = f"Error checking firewall: {e}"
        
        return firewall_info

    def test_dns_resolution(self) -> Dict[str, bool]:
        """Test DNS resolution"""
        test_domains = ['localhost', 'google.com', 'github.com']
        dns_results = {}
        
        for domain in test_domains:
            try:
                socket.gethostbyname(domain)
                dns_results[domain] = True
            except socket.gaierror:
                dns_results[domain] = False
        
        return dns_results

    def test_localhost_connectivity(self) -> Dict[str, bool]:
        """Test localhost connectivity"""
        localhost_tests = {
            '127.0.0.1': False,
            'localhost': False,
            self.local_ip: False
        }
        
        for host in localhost_tests.keys():
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    # Try to connect to a likely unused port
                    s.connect((host, 65432))
            except (socket.timeout, ConnectionRefusedError):
                # Connection refused is actually good - means host is reachable
                localhost_tests[host] = True
            except Exception:
                localhost_tests[host] = False
        
        return localhost_tests

    def check_common_ports(self) -> Dict[int, bool]:
        """Check status of commonly used ports"""
        common_ports = [80, 443, 8000, 8080, 3000, 5000, 9000]
        port_status = {}
        
        for port in common_ports:
            port_status[port] = self.is_port_available(port)
        
        return port_status

    def get_network_interfaces(self) -> List[Dict[str, str]]:
        """Get network interface information"""
        interfaces = []
        
        try:
            if self.system == 'Windows':
                result = subprocess.run(['ipconfig'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    # Parse ipconfig output (simplified)
                    interfaces.append({'name': 'Windows Interfaces', 'info': result.stdout[:200]})
            
            elif self.system in ['Linux', 'Darwin']:
                result = subprocess.run(['ifconfig'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    interfaces.append({'name': 'Unix Interfaces', 'info': result.stdout[:200]})
                    
        except Exception as e:
            interfaces.append({'name': 'Error', 'info': str(e)})
        
        return interfaces

    def fix_common_issues(self) -> List[str]:
        """Attempt to fix common network issues"""
        fixes_applied = []
        
        print("🔧 Attempting to fix common network issues...")
        
        # Fix 1: Flush DNS cache
        try:
            if self.system == 'Windows':
                subprocess.run(['ipconfig', '/flushdns'], check=True, capture_output=True)
                fixes_applied.append("DNS cache flushed")
            elif self.system == 'Darwin':
                subprocess.run(['sudo', 'dscacheutil', '-flushcache'], check=True, capture_output=True)
                fixes_applied.append("DNS cache flushed")
        except Exception:
            pass
        
        # Fix 2: Reset network stack (Windows)
        if self.system == 'Windows':
            try:
                subprocess.run(['netsh', 'winsock', 'reset'], check=True, capture_output=True)
                fixes_applied.append("Winsock reset")
            except Exception:
                pass
        
        # Fix 3: Check and suggest firewall rules
        firewall_status = self.check_firewall_status()
        if firewall_status['status'] == 'active':
            fixes_applied.append("Firewall detected - may need port exceptions")
        
        return fixes_applied

    def create_local_server(self, port: int = None) -> Optional[socket.socket]:
        """Create a local server for testing"""
        if port is None:
            available_ports = self.find_available_ports()
            if not available_ports:
                print("❌ No available ports found")
                return None
            port = available_ports[0]
        
        try:
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind(('localhost', port))
            server_socket.listen(1)
            
            print(f"✅ Local server created on port {port}")
            return server_socket
            
        except Exception as e:
            print(f"❌ Failed to create server on port {port}: {e}")
            return None

    def test_connection_to_game(self, game_process: str = None) -> Dict[str, any]:
        """Test connection capabilities to game applications"""
        test_results = {
            'keyboard_input': self.test_keyboard_input(),
            'window_focus': self.test_window_focus(),
            'process_detection': self.test_process_detection(game_process),
            'local_server': self.test_local_server_creation()
        }
        
        return test_results

    def test_keyboard_input(self) -> bool:
        """Test keyboard input capabilities"""
        try:
            import pyautogui
            # Test safe key press
            pyautogui.press('ctrl')
            return True
        except Exception:
            return False

    def test_window_focus(self) -> bool:
        """Test window focusing capabilities"""
        try:
            if self.system == 'Windows':
                import win32gui
                # Test getting foreground window
                win32gui.GetForegroundWindow()
                return True
            else:
                # For other systems, assume basic capability
                return True
        except Exception:
            return False

    def test_process_detection(self, process_name: str = None) -> Dict[str, any]:
        """Test process detection capabilities"""
        try:
            import psutil
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if process_name is None or process_name.lower() in proc.info['name'].lower():
                        processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return {'success': True, 'processes': processes[:5]}  # Limit to 5 for display
            
        except ImportError:
            return {'success': False, 'error': 'psutil not available'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_local_server_creation(self) -> bool:
        """Test local server creation"""
        server = self.create_local_server()
        if server:
            server.close()
            return True
        return False

    def generate_connection_report(self) -> str:
        """Generate comprehensive connection report"""
        print("📊 Generating connection report...")
        
        diagnostics = self.diagnose_network_issues()
        game_tests = self.test_connection_to_game()
        
        report = []
        report.append("🌐 NETWORK CONNECTION REPORT")
        report.append("=" * 50)
        report.append("")
        
        # Internet connectivity
        status = "✅ Connected" if diagnostics['internet_connection'] else "❌ No Connection"
        report.append(f"Internet: {status}")
        report.append(f"Local IP: {diagnostics['local_ip']}")
        report.append("")
        
        # Port availability
        report.append("🔌 Available Ports:")
        for port in diagnostics['available_ports'][:5]:
            report.append(f"   • Port {port}: Available")
        report.append("")
        
        # Firewall status
        fw_status = diagnostics['firewall_status']['status']
        report.append(f"🛡️  Firewall: {fw_status}")
        report.append("")
        
        # Game connection capabilities
        report.append("🎮 Game Connection Capabilities:")
        report.append(f"   • Keyboard Input: {'✅' if game_tests['keyboard_input'] else '❌'}")
        report.append(f"   • Window Focus: {'✅' if game_tests['window_focus'] else '❌'}")
        report.append(f"   • Process Detection: {'✅' if game_tests['process_detection']['success'] else '❌'}")
        report.append(f"   • Local Server: {'✅' if game_tests['local_server'] else '❌'}")
        report.append("")
        
        # Recommendations
        report.append("💡 RECOMMENDATIONS:")
        if not diagnostics['internet_connection']:
            report.append("   • Check internet connection for MediaPipe CDN")
        
        if diagnostics['firewall_status']['status'] == 'active':
            report.append("   • Consider adding firewall exceptions for gesture controller")
        
        if not game_tests['keyboard_input']:
            report.append("   • Install pyautogui: pip install pyautogui")
        
        if not game_tests['process_detection']['success']:
            report.append("   • Install psutil for better game detection: pip install psutil")
        
        report.append("")
        report.append("🚀 SUGGESTED CONNECTION METHODS:")
        report.append("   1. Direct Keyboard Input (Recommended)")
        report.append("   2. Window Focus Method")
        report.append("   3. Local TCP Server")
        report.append("   4. Named Pipes (Windows)")
        
        return "\n".join(report)

    def print_troubleshooting_guide(self):
        """Print comprehensive troubleshooting guide"""
        guide = """
🔧 NETWORK TROUBLESHOOTING GUIDE
================================

COMMON ISSUES & SOLUTIONS:

1. 🌐 INTERNET CONNECTION ISSUES
   Problem: MediaPipe CDN not accessible
   Solutions:
   • Check internet connection
   • Try different DNS servers (*******, *******)
   • Disable VPN temporarily
   • Check proxy settings

2. 🔌 PORT BINDING ISSUES
   Problem: "Address already in use"
   Solutions:
   • Use different port: python web_server.py --port 8001
   • Kill existing processes: netstat -ano | findstr :8000
   • Restart computer to clear port locks

3. 🛡️ FIREWALL BLOCKING
   Problem: Connections blocked by firewall
   Solutions:
   • Add exception for Python.exe
   • Allow port 8000-8010 in firewall
   • Temporarily disable firewall for testing

4. 🎮 GAME CONNECTION ISSUES
   Problem: Gestures not controlling game
   Solutions:
   • Ensure game window is focused
   • Run as administrator
   • Check game's input method compatibility
   • Use window focus connector instead of global keys

5. 📷 CAMERA ACCESS ISSUES
   Problem: Camera not accessible in browser
   Solutions:
   • Grant camera permissions in browser
   • Close other applications using camera
   • Try different browser (Chrome recommended)
   • Check camera drivers

ADVANCED SOLUTIONS:

• Use offline mode: Download MediaPipe models locally
• Network isolation: Use localhost-only connections
• Process injection: Direct game memory access (advanced)
• Virtual input: Use virtual keyboard drivers

TESTING COMMANDS:

• Test port: telnet localhost 8000
• Check processes: netstat -ano
• Test camera: python -c "import cv2; cv2.VideoCapture(0)"
• Network info: ipconfig /all (Windows) or ifconfig (Unix)
"""
        print(guide)


def main():
    """Run network diagnostics and troubleshooting"""
    print("🌐 Network Manager - Diagnostics & Troubleshooting")
    print("=" * 60)
    
    manager = NetworkManager()
    
    # Run full diagnostics
    report = manager.generate_connection_report()
    print(report)
    
    # Apply fixes
    fixes = manager.fix_common_issues()
    if fixes:
        print("\n🔧 Applied fixes:")
        for fix in fixes:
            print(f"   • {fix}")
    
    # Show troubleshooting guide
    print("\n" + "=" * 60)
    manager.print_troubleshooting_guide()


if __name__ == "__main__":
    main()
