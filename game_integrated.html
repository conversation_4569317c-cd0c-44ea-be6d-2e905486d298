<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Game-Integrated Gesture Controller</title>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/control_utils/control_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils/drawing_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/hands/hands.js"></script>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; margin: 0; padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .video-container { 
            position: relative; background: rgba(0,0,0,0.8); 
            border-radius: 15px; overflow: hidden; margin-bottom: 20px;
        }
        #videoElement { width: 100%; height: auto; }
        #canvasElement { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }
        .controls { 
            display: flex; gap: 10px; justify-content: center; 
            padding: 15px; background: rgba(0,0,0,0.5);
        }
        .btn { 
            padding: 10px 20px; border: none; border-radius: 25px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white; font-weight: bold; cursor: pointer;
        }
        .btn:hover { transform: translateY(-2px); }
        .btn.active { background: linear-gradient(45deg, #00d2d3, #54a0ff); }
        .status { 
            text-align: center; padding: 20px; 
            background: rgba(255,255,255,0.1); border-radius: 10px;
        }
        .gesture-display { font-size: 2em; margin: 10px 0; }
        .game-status { 
            position: fixed; top: 20px; right: 20px; 
            padding: 10px; background: rgba(0,0,0,0.8); border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="game-status" id="gameStatus">🔴 Disconnected</div>
    
    <div class="container">
        <div class="header">
            <h1>🎮 Game-Integrated Gesture Controller</h1>
            <p>Direct game control with real-time gesture recognition</p>
        </div>
        
        <div class="video-container">
            <video id="videoElement" autoplay muted playsinline></video>
            <canvas id="canvasElement"></canvas>
            <div class="controls">
                <button class="btn" id="startBtn">🎥 Start Camera</button>
                <button class="btn" id="connectBtn">🎮 Connect to Game</button>
                <button class="btn" id="testBtn">🧪 Test Connection</button>
            </div>
        </div>
        
        <div class="status">
            <div class="gesture-display" id="gestureDisplay">👋 Show your hand</div>
            <div id="connectionStatus">Ready to connect to game</div>
        </div>
    </div>

    <script>
        class GameIntegratedController {
            constructor() {
                this.hands = null;
                this.camera = null;
                this.isConnected = false;
                this.bridgeUrl = 'http://localhost:8001/gesture';
                
                this.init();
            }
            
            async init() {
                await this.initializeMediaPipe();
                this.setupEventListeners();
            }
            
            async initializeMediaPipe() {
                this.hands = new Hands({
                    locateFile: (file) => `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`
                });
                
                this.hands.setOptions({
                    maxNumHands: 1,
                    modelComplexity: 1,
                    minDetectionConfidence: 0.7,
                    minTrackingConfidence: 0.5
                });
                
                this.hands.onResults(this.onResults.bind(this));
            }
            
            setupEventListeners() {
                document.getElementById('startBtn').addEventListener('click', () => this.startCamera());
                document.getElementById('connectBtn').addEventListener('click', () => this.connectToGame());
                document.getElementById('testBtn').addEventListener('click', () => this.testConnection());
            }
            
            async startCamera() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                    const videoElement = document.getElementById('videoElement');
                    videoElement.srcObject = stream;
                    
                    this.camera = new Camera(videoElement, {
                        onFrame: async () => {
                            await this.hands.send({image: videoElement});
                        },
                        width: 640,
                        height: 480
                    });
                    
                    await this.camera.start();
                    document.getElementById('startBtn').classList.add('active');
                    
                } catch (error) {
                    alert('Camera access denied or not available');
                }
            }
            
            async connectToGame() {
                try {
                    const response = await fetch(this.bridgeUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ type: 'connect_test' })
                    });
                    
                    if (response.ok) {
                        this.isConnected = true;
                        document.getElementById('gameStatus').textContent = '🟢 Connected';
                        document.getElementById('connectBtn').classList.add('active');
                        document.getElementById('connectionStatus').textContent = 'Connected to game bridge';
                    } else {
                        throw new Error('Connection failed');
                    }
                    
                } catch (error) {
                    alert('Failed to connect to game bridge. Make sure the bridge server is running.');
                }
            }
            
            async testConnection() {
                if (!this.isConnected) {
                    alert('Please connect to game first');
                    return;
                }
                
                const testGestures = ['open_palm', 'fist', 'point_left', 'point_right'];
                
                for (const gesture of testGestures) {
                    await this.sendGesture(gesture, 0.9);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
                alert('Test sequence completed!');
            }
            
            onResults(results) {
                const canvas = document.getElementById('canvasElement');
                const ctx = canvas.getContext('2d');
                
                canvas.width = document.getElementById('videoElement').videoWidth;
                canvas.height = document.getElementById('videoElement').videoHeight;
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                if (results.multiHandLandmarks && results.multiHandLandmarks.length > 0) {
                    const landmarks = results.multiHandLandmarks[0];
                    
                    // Draw hand landmarks
                    this.drawHandLandmarks(ctx, landmarks);
                    
                    // Recognize gesture
                    const [gesture, confidence] = this.recognizeGesture(landmarks);
                    
                    // Update display
                    document.getElementById('gestureDisplay').textContent = 
                        `${this.getGestureEmoji(gesture)} ${gesture} (${Math.round(confidence * 100)}%)`;
                    
                    // Send to game if connected
                    if (this.isConnected && confidence > 0.7) {
                        this.sendGesture(gesture, confidence);
                    }
                }
            }
            
            drawHandLandmarks(ctx, landmarks) {
                ctx.strokeStyle = '#00ff00';
                ctx.lineWidth = 2;
                ctx.fillStyle = '#ff0000';
                
                // Draw connections (simplified)
                const connections = [
                    [0, 1], [1, 2], [2, 3], [3, 4],  // Thumb
                    [0, 5], [5, 6], [6, 7], [7, 8],  // Index
                    [0, 9], [9, 10], [10, 11], [11, 12],  // Middle
                    [0, 13], [13, 14], [14, 15], [15, 16],  // Ring
                    [0, 17], [17, 18], [18, 19], [19, 20]   // Pinky
                ];
                
                connections.forEach(([start, end]) => {
                    const startPoint = landmarks[start];
                    const endPoint = landmarks[end];
                    
                    ctx.beginPath();
                    ctx.moveTo(startPoint.x * ctx.canvas.width, startPoint.y * ctx.canvas.height);
                    ctx.lineTo(endPoint.x * ctx.canvas.width, endPoint.y * ctx.canvas.height);
                    ctx.stroke();
                });
                
                // Draw landmarks
                landmarks.forEach(landmark => {
                    ctx.beginPath();
                    ctx.arc(landmark.x * ctx.canvas.width, landmark.y * ctx.canvas.height, 5, 0, 2 * Math.PI);
                    ctx.fill();
                });
            }
            
            recognizeGesture(landmarks) {
                // Simple gesture recognition
                const fingers = this.getFingerStates(landmarks);
                const fingerCount = fingers.filter(Boolean).length;
                
                if (fingerCount === 0) return ['fist', 0.9];
                if (fingerCount === 5) return ['open_palm', 0.9];
                if (fingers[1] && !fingers[2] && !fingers[3] && !fingers[4]) {
                    // Check pointing direction
                    const indexTip = landmarks[8];
                    const wrist = landmarks[0];
                    if (indexTip.x < wrist.x - 0.1) return ['point_left', 0.8];
                    if (indexTip.x > wrist.x + 0.1) return ['point_right', 0.8];
                    return ['point', 0.8];
                }
                if (fingers[1] && fingers[2] && !fingers[3] && !fingers[4]) return ['peace_sign', 0.8];
                if (fingers[0] && !fingers[1] && !fingers[2] && !fingers[3] && !fingers[4]) return ['thumbs_up', 0.8];
                
                return ['unknown', 0.3];
            }
            
            getFingerStates(landmarks) {
                const fingers = [];
                
                // Thumb
                fingers.push(landmarks[4].x > landmarks[3].x);
                
                // Other fingers
                [8, 12, 16, 20].forEach((tip, i) => {
                    const pip = [6, 10, 14, 18][i];
                    fingers.push(landmarks[tip].y < landmarks[pip].y);
                });
                
                return fingers;
            }
            
            getGestureEmoji(gesture) {
                const emojis = {
                    'open_palm': '✋',
                    'fist': '✊',
                    'point': '👉',
                    'point_left': '👈',
                    'point_right': '👉',
                    'peace_sign': '✌️',
                    'thumbs_up': '👍',
                    'unknown': '❓'
                };
                return emojis[gesture] || '❓';
            }
            
            async sendGesture(gesture, confidence) {
                if (!this.isConnected) return;
                
                try {
                    await fetch(this.bridgeUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            gesture: gesture,
                            confidence: confidence,
                            timestamp: Date.now()
                        })
                    });
                } catch (error) {
                    console.error('Failed to send gesture:', error);
                }
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new GameIntegratedController();
        });
    </script>
</body>
</html>