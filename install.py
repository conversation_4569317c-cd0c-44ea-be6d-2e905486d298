#!/usr/bin/env python3
"""
🚀 AI Gesture Controller Installation Script
Automated setup for the revolutionary gesture control system
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def print_banner():
    """Print installation banner"""
    print("=" * 60)
    print("🚀 AI GESTURE CONTROLLER INSTALLATION")
    print("Revolutionary Gaming Control System Setup")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python 3.9+ is required for advanced features")
        print("💡 Consider upgrading Python for the best experience")
        return False
    elif version.minor >= 11:
        print("✅ Excellent! Python 3.11+ detected - all features supported")
        return True
    else:
        print("⚠️  Python 3.9-3.10 detected - most features supported")
        return True

def detect_system():
    """Detect operating system and architecture"""
    system = platform.system()
    architecture = platform.machine()
    print(f"💻 System: {system} {architecture}")
    
    if system == "Windows":
        print("🪟 Windows detected - using PowerShell commands")
    elif system == "Darwin":
        print("🍎 macOS detected - using Unix commands")
    elif system == "Linux":
        print("🐧 Linux detected - using Unix commands")
    
    return system, architecture

def install_requirements(level="simple"):
    """Install Python requirements"""
    requirements_file = f"requirements_{level}.txt"
    
    if not Path(requirements_file).exists():
        print(f"❌ Requirements file {requirements_file} not found")
        return False
    
    print(f"📦 Installing {level} requirements...")
    
    try:
        # Upgrade pip first
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # Install requirements
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", requirements_file], 
                              check=True, capture_output=True, text=True)
        
        print("✅ Requirements installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def test_camera():
    """Test camera availability"""
    print("📷 Testing camera access...")
    
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print("✅ Camera test successful!")
                cap.release()
                return True
            else:
                print("⚠️  Camera detected but unable to read frames")
                cap.release()
                return False
        else:
            print("❌ No camera detected or camera access denied")
            return False
            
    except ImportError:
        print("⚠️  OpenCV not installed - skipping camera test")
        return True
    except Exception as e:
        print(f"❌ Camera test failed: {e}")
        return False

def run_demo():
    """Run the gesture demo"""
    print("🎮 Starting gesture recognition demo...")
    
    try:
        subprocess.run([sys.executable, "gesture_demo.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ Demo failed to start")
        return False
    except KeyboardInterrupt:
        print("\n🛑 Demo stopped by user")
        return True
    
    return True

def main():
    """Main installation process"""
    print_banner()
    
    # Check Python version
    if not check_python_version():
        print("\n💡 Installation can continue but some features may not work")
        response = input("Continue anyway? (y/N): ").lower()
        if response != 'y':
            print("Installation cancelled")
            return
    
    # Detect system
    system, arch = detect_system()
    print()
    
    # Choose installation level
    print("📋 Choose installation level:")
    print("1. Simple (Recommended) - Core features only")
    print("2. Advanced - Full AI features with all dependencies")
    print("3. Demo Only - Just run the demo")
    
    while True:
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            level = "simple"
            break
        elif choice == "2":
            level = "advanced"
            break
        elif choice == "3":
            level = "demo"
            break
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")
    
    print()
    
    # Install requirements
    if level != "demo":
        if not install_requirements(level):
            print("❌ Installation failed!")
            return
        print()
    
    # Test camera
    if not test_camera():
        print("⚠️  Camera issues detected - gesture control may not work properly")
        print("💡 Make sure your camera is connected and not used by other applications")
    print()
    
    # Installation complete
    print("🎉 INSTALLATION COMPLETE!")
    print()
    
    if level == "simple":
        print("🚀 You can now run:")
        print("   python smart_gesture_controller.py")
        print("   python gesture_demo.py")
    elif level == "advanced":
        print("🚀 You can now run:")
        print("   python ai_gesture_controller.py")
        print("   python smart_gesture_controller.py")
        print("   python gesture_demo.py")
    
    print()
    print("📖 Check README.md for detailed usage instructions")
    
    # Ask to run demo
    if level != "demo":
        response = input("\n🎮 Would you like to run the gesture demo now? (Y/n): ").lower()
        if response != 'n':
            print()
            run_demo()
    else:
        run_demo()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Installation cancelled by user")
    except Exception as e:
        print(f"\n❌ Installation error: {e}")
        print("Please check the error and try again")
