#!/usr/bin/env python3
"""
🚀 AI Gesture Controller Web Server
Simple HTTP server to serve the web-based gesture controller
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler to serve files with proper MIME types"""
    
    def end_headers(self):
        # Add CORS headers for MediaPipe
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        super().end_headers()
    
    def guess_type(self, path):
        """Override to handle JavaScript files properly"""
        mimetype, encoding = super().guess_type(path)
        if path.endswith('.js'):
            return 'application/javascript', encoding
        return mimetype, encoding

def find_free_port(start_port=8000, max_attempts=100):
    """Find a free port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"Could not find a free port in range {start_port}-{start_port + max_attempts}")

def main():
    """Start the web server"""
    print("🚀 AI Gesture Controller Web Server")
    print("=" * 50)
    
    # Check if required files exist
    required_files = ['index.html', 'gesture-controller.js']
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        print("Please make sure all files are in the current directory.")
        return
    
    try:
        # Find a free port
        port = find_free_port()
        
        # Create server
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            server_url = f"http://localhost:{port}"
            
            print(f"🌐 Server starting at: {server_url}")
            print(f"📁 Serving files from: {os.getcwd()}")
            print()
            print("🎮 Features available:")
            print("   • Real-time gesture recognition")
            print("   • AI-powered hand tracking")
            print("   • Performance analytics")
            print("   • Multiple game profiles")
            print("   • Browser-based - no installation needed!")
            print()
            print("🔧 Controls:")
            print("   • Click 'Start Camera' to begin")
            print("   • Use keyboard shortcuts:")
            print("     - Space: Start/Stop camera")
            print("     - R: Reset statistics")
            print("     - F: Toggle fullscreen")
            print()
            print("⚠️  Note: Camera access required for gesture recognition")
            print("🛑 Press Ctrl+C to stop the server")
            print()
            
            # Try to open browser automatically
            try:
                print("🌐 Opening browser automatically...")
                webbrowser.open(server_url)
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print(f"Please manually open: {server_url}")
            
            print(f"✅ Server running on port {port}")
            print("=" * 50)
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
        print("Please check the error and try again")

if __name__ == "__main__":
    main()
