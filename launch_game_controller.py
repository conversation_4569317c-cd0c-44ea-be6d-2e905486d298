#!/usr/bin/env python3
"""
🚀 Game Controller Launcher
Automatic setup with network troubleshooting and game detection
"""

import subprocess
import sys
import time
import os
import webbrowser
from pathlib import Path

def print_banner():
    """Print startup banner"""
    print("🚀 AI GESTURE GAME CONTROLLER LAUNCHER")
    print("=" * 50)
    print("🎮 Automatic setup with game detection")
    print("🌐 Network troubleshooting included")
    print("🔧 Multiple connection methods")
    print("=" * 50)
    print()

def check_requirements():
    """Check if required files and packages exist"""
    print("🔍 Checking requirements...")
    
    # Check required files
    required_files = [
        'smart_gesture_controller.py',
        'game_connector.py', 
        'network_manager.py',
        'index.html',
        'gesture-controller.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    
    # Check Python packages
    required_packages = ['cv2', 'mediapipe', 'pyautogui']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"⚠️  Missing packages: {', '.join(missing_packages)}")
        print("💡 Installing missing packages...")
        
        package_map = {
            'cv2': 'opencv-python',
            'mediapipe': 'mediapipe',
            'pyautogui': 'pyautogui'
        }
        
        for package in missing_packages:
            pip_package = package_map.get(package, package)
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', pip_package], 
                             check=True, capture_output=True)
                print(f"✅ Installed {pip_package}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {pip_package}")
                return False
    
    print("✅ All requirements satisfied")
    return True

def detect_games():
    """Detect running games"""
    print("🎮 Detecting running games...")
    
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['tasklist'], capture_output=True, text=True)
            processes = result.stdout.lower()
        else:  # Unix-like
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            processes = result.stdout.lower()
        
        detected_games = []
        game_patterns = {
            'Temple Run': ['temple', 'templerun'],
            'Subway Surfers': ['subway', 'surfers'],
            'Chrome Browser': ['chrome', 'chromium'],
            'Firefox Browser': ['firefox'],
            'Edge Browser': ['msedge', 'edge']
        }
        
        for game_name, patterns in game_patterns.items():
            for pattern in patterns:
                if pattern in processes:
                    detected_games.append(game_name)
                    break
        
        if detected_games:
            print(f"🎯 Detected games: {', '.join(detected_games)}")
        else:
            print("ℹ️  No specific games detected (will work with any game)")
        
        return detected_games
        
    except Exception as e:
        print(f"⚠️  Game detection failed: {e}")
        return []

def run_network_diagnostics():
    """Run network diagnostics and fix issues"""
    print("🌐 Running network diagnostics...")
    
    try:
        result = subprocess.run([sys.executable, 'network_manager.py'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Network diagnostics completed")
            # Print key findings
            lines = result.stdout.split('\n')
            for line in lines:
                if any(keyword in line.lower() for keyword in ['available ports', 'internet', 'firewall']):
                    print(f"   {line}")
        else:
            print("⚠️  Network diagnostics had issues")
            
    except subprocess.TimeoutExpired:
        print("⚠️  Network diagnostics timed out")
    except Exception as e:
        print(f"⚠️  Network diagnostics failed: {e}")

def choose_connection_method(detected_games):
    """Choose the best connection method based on detected games"""
    print("\n🔧 Choosing connection method...")
    
    # If browsers are detected, suggest web version
    browsers = ['Chrome Browser', 'Firefox Browser', 'Edge Browser']
    if any(browser in detected_games for browser in browsers):
        print("🌐 Browser detected - Web version recommended")
        return 'web'
    
    # If specific games detected, use desktop version
    games = ['Temple Run', 'Subway Surfers']
    if any(game in detected_games for game in games):
        print("🎮 Desktop games detected - Smart controller recommended")
        return 'desktop'
    
    # Default to desktop version
    print("🖥️  Using desktop version (most compatible)")
    return 'desktop'

def launch_web_version():
    """Launch web-based controller"""
    print("🌐 Starting web-based gesture controller...")
    
    try:
        # Start web server
        server_process = subprocess.Popen([sys.executable, 'web_server.py'])
        
        # Wait a moment for server to start
        time.sleep(2)
        
        # Open browser
        webbrowser.open('http://localhost:8000')
        
        print("✅ Web controller started at http://localhost:8000")
        print("🎮 Click 'Start Camera' in the browser to begin")
        print("🛑 Press Ctrl+C to stop the server")
        
        # Wait for user to stop
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping web server...")
            server_process.terminate()
            
    except Exception as e:
        print(f"❌ Failed to start web version: {e}")

def launch_desktop_version():
    """Launch desktop controller"""
    print("🖥️  Starting desktop gesture controller...")
    
    try:
        # Start smart gesture controller
        subprocess.run([sys.executable, 'smart_gesture_controller.py'])
        
    except KeyboardInterrupt:
        print("\n🛑 Desktop controller stopped")
    except Exception as e:
        print(f"❌ Failed to start desktop version: {e}")

def show_usage_instructions(method):
    """Show usage instructions"""
    print("\n📖 USAGE INSTRUCTIONS")
    print("=" * 30)
    
    if method == 'web':
        print("🌐 Web Version:")
        print("1. Browser will open automatically")
        print("2. Click 'Start Camera' button")
        print("3. Allow camera access when prompted")
        print("4. Show your hand to the camera")
        print("5. Try different gestures:")
        print("   ✋ Open Palm → Jump")
        print("   ✊ Fist → Slide")
        print("   👉 Point → Turn")
        print("   ✌️ Peace → Pause")
    else:
        print("🖥️  Desktop Version:")
        print("1. Camera window will open")
        print("2. Position your hand in view")
        print("3. Launch your game")
        print("4. Use gestures to control:")
        print("   ✋ Open Palm → Up/Jump")
        print("   ✊ Fist → Down/Slide")
        print("   👈👉 Point → Left/Right")
        print("   ✌️ Peace → Pause")
    
    print("\n💡 Tips:")
    print("• Ensure good lighting")
    print("• Keep hand centered in frame")
    print("• Make clear, distinct gestures")
    print("• Check confidence scores")
    print()

def main():
    """Main launcher function"""
    print_banner()
    
    # Check requirements
    if not check_requirements():
        print("❌ Requirements check failed")
        input("Press Enter to exit...")
        return
    
    print()
    
    # Detect games
    detected_games = detect_games()
    print()
    
    # Run network diagnostics
    run_network_diagnostics()
    print()
    
    # Choose connection method
    method = choose_connection_method(detected_games)
    print()
    
    # Show instructions
    show_usage_instructions(method)
    
    # Ask user to proceed
    response = input("🚀 Ready to start? (Y/n): ").lower()
    if response == 'n':
        print("👋 Goodbye!")
        return
    
    print()
    
    # Launch appropriate version
    if method == 'web':
        launch_web_version()
    else:
        launch_desktop_version()
    
    print("\n✅ Session completed!")
    print("🎮 Thanks for using AI Gesture Controller!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Launcher interrupted by user")
    except Exception as e:
        print(f"\n❌ Launcher error: {e}")
        input("Press Enter to exit...")
