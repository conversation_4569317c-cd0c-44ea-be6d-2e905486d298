#!/usr/bin/env python3
"""
🎮 Smart Multi-Modal Gesture Controller
Next-generation gaming control with AI-powered features

Revolutionary Features:
- Advanced gesture recognition with ML
- Real-time performance analytics
- Adaptive sensitivity system
- Multi-game profiles
- Voice command integration
- 3D spatial tracking
- Smart cooldown management
"""

import cv2
import numpy as np
import mediapipe as mp
import pyautogui
import time
import json
import math
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
from collections import deque
import threading

@dataclass
class GameProfile:
    """Game-specific control configuration"""
    name: str
    gestures: Dict[str, str]
    sensitivity: float
    cooldown_frames: int
    voice_enabled: bool

@dataclass
class PerformanceStats:
    """Real-time performance tracking"""
    gestures_performed: int
    accuracy_score: float
    session_time: float
    avg_reaction_time: float
    best_streak: int

class SmartGestureController:
    """Revolutionary gesture controller with AI features"""
    
    def __init__(self):
        print("🚀 Initializing Smart Gesture Controller...")
        
        # MediaPipe setup with optimized settings
        self.mp_hands = mp.solutions.hands
        self.mp_face = mp.solutions.face_mesh
        self.mp_draw = mp.solutions.drawing_utils
        
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.8,
            min_tracking_confidence=0.7
        )
        
        self.face_mesh = self.mp_face.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.6
        )
        
        # Advanced tracking systems
        self.gesture_history = deque(maxlen=20)
        self.position_history = deque(maxlen=15)
        self.velocity_buffer = deque(maxlen=5)
        
        # Performance analytics
        self.performance = PerformanceStats(0, 0.0, 0.0, 0.0, 0)
        self.session_start = time.time()
        self.last_gesture_time = time.time()
        self.current_streak = 0
        
        # Adaptive systems
        self.adaptive_sensitivity = 1.0
        self.gesture_confidence_threshold = 0.7
        self.smart_cooldown = SmartCooldownManager()
        
        # Game profiles
        self.game_profiles = self.initialize_game_profiles()
        self.current_profile = "temple_run"
        
        # Camera setup with high performance
        self.cap = cv2.VideoCapture(0)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        self.cap.set(cv2.CAP_PROP_FPS, 60)
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        
        # Voice recognition (simplified)
        self.voice_commands = {
            "jump": "up", "slide": "down", "left": "left", 
            "right": "right", "pause": "space", "boost": "shift"
        }
        
        print("✅ Smart Gesture Controller ready!")
        print("🎮 Advanced features: AI gestures, performance tracking, adaptive controls")

    def initialize_game_profiles(self) -> Dict[str, GameProfile]:
        """Initialize game-specific profiles"""
        return {
            "temple_run": GameProfile(
                name="Temple Run",
                gestures={
                    "open_palm": "up",      # Jump
                    "fist": "down",         # Slide
                    "swipe_left": "left",   # Turn left
                    "swipe_right": "right", # Turn right
                    "peace_sign": "space",  # Pause
                    "thumbs_up": "shift"    # Boost
                },
                sensitivity=1.0,
                cooldown_frames=8,
                voice_enabled=True
            ),
            "subway_surfers": GameProfile(
                name="Subway Surfers",
                gestures={
                    "open_palm": "up",
                    "fist": "down", 
                    "point_left": "left",
                    "point_right": "right",
                    "two_fingers": "space"
                },
                sensitivity=1.2,
                cooldown_frames=6,
                voice_enabled=True
            ),
            "custom": GameProfile(
                name="Custom Game",
                gestures={
                    "open_palm": "w",
                    "fist": "s",
                    "point_left": "a",
                    "point_right": "d",
                    "peace_sign": "space"
                },
                sensitivity=1.0,
                cooldown_frames=10,
                voice_enabled=False
            )
        }

    def advanced_gesture_recognition(self, hand_landmarks, face_landmarks=None) -> Tuple[str, float]:
        """AI-powered gesture recognition with confidence scoring"""
        if not hand_landmarks:
            return "none", 0.0
        
        # Extract hand features
        landmarks = [(lm.x, lm.y, lm.z) for lm in hand_landmarks.landmark]
        
        # Advanced gesture analysis
        fingers_up = self.analyze_finger_states(hand_landmarks)
        hand_shape = self.analyze_hand_shape(landmarks)
        hand_orientation = self.calculate_hand_orientation(landmarks)
        gesture_dynamics = self.analyze_gesture_dynamics(landmarks)
        
        # Multi-factor gesture recognition
        gesture, confidence = self.classify_gesture(
            fingers_up, hand_shape, hand_orientation, gesture_dynamics
        )
        
        # Temporal smoothing for stability
        gesture = self.apply_temporal_smoothing(gesture, confidence)
        
        return gesture, confidence

    def analyze_finger_states(self, hand_landmarks) -> List[bool]:
        """Analyze which fingers are extended"""
        landmarks = hand_landmarks.landmark
        fingers = []
        
        # Thumb (special case - check x-axis for left/right hand)
        thumb_extended = abs(landmarks[4].x - landmarks[3].x) > 0.05
        fingers.append(thumb_extended)
        
        # Other fingers (check y-axis)
        finger_tips = [8, 12, 16, 20]  # Index, Middle, Ring, Pinky
        finger_pips = [6, 10, 14, 18]
        
        for tip, pip in zip(finger_tips, finger_pips):
            extended = landmarks[tip].y < landmarks[pip].y - 0.02
            fingers.append(extended)
        
        return fingers

    def analyze_hand_shape(self, landmarks: List[Tuple[float, float, float]]) -> Dict[str, float]:
        """Analyze overall hand shape characteristics"""
        wrist = landmarks[0]
        
        # Calculate hand span
        thumb_tip = landmarks[4]
        pinky_tip = landmarks[20]
        hand_span = math.sqrt((thumb_tip[0] - pinky_tip[0])**2 + (thumb_tip[1] - pinky_tip[1])**2)
        
        # Calculate hand length
        middle_tip = landmarks[12]
        hand_length = math.sqrt((middle_tip[0] - wrist[0])**2 + (middle_tip[1] - wrist[1])**2)
        
        # Calculate finger spread
        finger_spread = self.calculate_finger_spread(landmarks)
        
        # Calculate hand curvature
        hand_curvature = self.calculate_hand_curvature(landmarks)
        
        return {
            "span": hand_span,
            "length": hand_length,
            "spread": finger_spread,
            "curvature": hand_curvature
        }

    def calculate_finger_spread(self, landmarks: List[Tuple[float, float, float]]) -> float:
        """Calculate average spread between fingers"""
        finger_tips = [landmarks[i] for i in [8, 12, 16, 20]]
        total_spread = 0
        
        for i in range(len(finger_tips) - 1):
            spread = math.sqrt(
                (finger_tips[i][0] - finger_tips[i+1][0])**2 + 
                (finger_tips[i][1] - finger_tips[i+1][1])**2
            )
            total_spread += spread
        
        return total_spread / (len(finger_tips) - 1)

    def calculate_hand_curvature(self, landmarks: List[Tuple[float, float, float]]) -> float:
        """Calculate hand curvature (how curved/flat the hand is)"""
        # Use knuckles to determine curvature
        knuckles = [landmarks[i] for i in [5, 9, 13, 17]]  # MCP joints
        
        # Calculate deviation from straight line
        if len(knuckles) < 2:
            return 0.0
        
        # Simple curvature approximation
        total_deviation = 0
        for i in range(1, len(knuckles) - 1):
            # Calculate how much each knuckle deviates from the line between first and last
            deviation = self.point_to_line_distance(knuckles[i], knuckles[0], knuckles[-1])
            total_deviation += deviation
        
        return total_deviation / max(1, len(knuckles) - 2)

    def point_to_line_distance(self, point: Tuple[float, float, float], 
                              line_start: Tuple[float, float, float], 
                              line_end: Tuple[float, float, float]) -> float:
        """Calculate distance from point to line"""
        # 2D distance calculation
        x0, y0 = point[0], point[1]
        x1, y1 = line_start[0], line_start[1]
        x2, y2 = line_end[0], line_end[1]
        
        # Line equation: ax + by + c = 0
        a = y2 - y1
        b = x1 - x2
        c = x2 * y1 - x1 * y2
        
        # Distance formula
        distance = abs(a * x0 + b * y0 + c) / math.sqrt(a**2 + b**2)
        return distance

    def calculate_hand_orientation(self, landmarks: List[Tuple[float, float, float]]) -> float:
        """Calculate hand orientation angle"""
        wrist = landmarks[0]
        middle_mcp = landmarks[9]  # Middle finger MCP joint
        
        angle = math.atan2(middle_mcp[1] - wrist[1], middle_mcp[0] - wrist[0])
        return math.degrees(angle)

    def analyze_gesture_dynamics(self, landmarks: List[Tuple[float, float, float]]) -> Dict[str, float]:
        """Analyze gesture movement dynamics"""
        if len(self.position_history) < 2:
            return {"velocity": 0.0, "acceleration": 0.0, "stability": 1.0}
        
        # Current position (use index finger tip)
        current_pos = landmarks[8]
        
        # Calculate velocity
        prev_pos = self.position_history[-1]
        dt = 1/60  # Assuming 60 FPS
        velocity = math.sqrt(
            (current_pos[0] - prev_pos[0])**2 + 
            (current_pos[1] - prev_pos[1])**2
        ) / dt
        
        # Calculate acceleration
        self.velocity_buffer.append(velocity)
        acceleration = 0.0
        if len(self.velocity_buffer) >= 2:
            acceleration = (self.velocity_buffer[-1] - self.velocity_buffer[-2]) / dt
        
        # Calculate stability (how steady the hand is)
        recent_positions = list(self.position_history)[-5:]
        if len(recent_positions) > 1:
            position_variance = np.var([pos[0] for pos in recent_positions]) + \
                              np.var([pos[1] for pos in recent_positions])
            stability = 1.0 / (1.0 + position_variance * 100)
        else:
            stability = 1.0
        
        return {
            "velocity": velocity,
            "acceleration": acceleration,
            "stability": stability
        }

    def classify_gesture(self, fingers_up: List[bool], hand_shape: Dict[str, float], 
                        orientation: float, dynamics: Dict[str, float]) -> Tuple[str, float]:
        """Advanced gesture classification with confidence scoring"""
        
        # Count extended fingers
        finger_count = sum(fingers_up)
        
        # Base confidence on stability and hand shape consistency
        base_confidence = dynamics["stability"] * 0.7 + 0.3
        
        # Gesture classification logic
        if finger_count == 0:  # Fist
            confidence = base_confidence * (1.0 - hand_shape["spread"] * 2)
            return "fist", min(confidence, 1.0)
        
        elif finger_count == 5:  # Open palm
            confidence = base_confidence * hand_shape["spread"]
            return "open_palm", min(confidence, 1.0)
        
        elif fingers_up == [False, True, False, False, False]:  # Pointing
            if -45 <= orientation <= 45:
                return "point_right", base_confidence * 0.9
            elif 135 <= orientation <= 225:
                return "point_left", base_confidence * 0.9
            else:
                return "point", base_confidence * 0.7
        
        elif fingers_up == [False, True, True, False, False]:  # Peace sign
            return "peace_sign", base_confidence * 0.8
        
        elif fingers_up == [True, False, False, False, False]:  # Thumbs up
            return "thumbs_up", base_confidence * 0.8
        
        elif fingers_up == [False, True, True, True, False]:  # Three fingers
            return "three_fingers", base_confidence * 0.7
        
        elif finger_count == 2 and fingers_up[0] and fingers_up[4]:  # Rock sign
            return "rock_sign", base_confidence * 0.8
        
        # Motion-based gestures
        if dynamics["velocity"] > 0.5:
            if abs(orientation) < 30:  # Horizontal movement
                if dynamics["acceleration"] > 0:
                    return "swipe_right", base_confidence * 0.6
                else:
                    return "swipe_left", base_confidence * 0.6
            elif 60 < abs(orientation) < 120:  # Vertical movement
                if orientation > 0:
                    return "swipe_down", base_confidence * 0.6
                else:
                    return "swipe_up", base_confidence * 0.6
        
        return "unknown", 0.3

    def apply_temporal_smoothing(self, gesture: str, confidence: float) -> str:
        """Apply temporal smoothing to reduce gesture jitter"""
        self.gesture_history.append((gesture, confidence))
        
        if len(self.gesture_history) < 3:
            return gesture
        
        # Get recent gestures
        recent_gestures = list(self.gesture_history)[-5:]
        
        # Count occurrences of each gesture
        gesture_counts = {}
        total_confidence = 0
        
        for g, c in recent_gestures:
            if g not in gesture_counts:
                gesture_counts[g] = {"count": 0, "confidence": 0}
            gesture_counts[g]["count"] += 1
            gesture_counts[g]["confidence"] += c
            total_confidence += c
        
        # Find most consistent gesture
        best_gesture = gesture
        best_score = 0
        
        for g, data in gesture_counts.items():
            consistency = data["count"] / len(recent_gestures)
            avg_confidence = data["confidence"] / data["count"]
            score = consistency * avg_confidence
            
            if score > best_score:
                best_score = score
                best_gesture = g
        
        # Only return smoothed gesture if confidence is high enough
        if best_score > self.gesture_confidence_threshold:
            return best_gesture
        else:
            return gesture

    def update_performance_stats(self, gesture: str, confidence: float):
        """Update real-time performance statistics"""
        current_time = time.time()
        
        # Update basic stats
        self.performance.gestures_performed += 1
        self.performance.session_time = current_time - self.session_start
        
        # Calculate reaction time
        reaction_time = current_time - self.last_gesture_time
        self.performance.avg_reaction_time = (
            (self.performance.avg_reaction_time * (self.performance.gestures_performed - 1) + reaction_time) /
            self.performance.gestures_performed
        )
        
        # Update accuracy based on confidence
        if confidence > 0.8:
            self.current_streak += 1
            self.performance.best_streak = max(self.performance.best_streak, self.current_streak)
        else:
            self.current_streak = 0
        
        # Calculate overall accuracy
        accuracy_factor = min(confidence, 1.0)
        self.performance.accuracy_score = (
            (self.performance.accuracy_score * (self.performance.gestures_performed - 1) + accuracy_factor) /
            self.performance.gestures_performed
        )
        
        self.last_gesture_time = current_time

    def adapt_sensitivity(self):
        """Dynamically adapt sensitivity based on performance"""
        if self.performance.gestures_performed < 10:
            return
        
        # Increase sensitivity if accuracy is high
        if self.performance.accuracy_score > 0.85:
            self.adaptive_sensitivity = min(1.5, self.adaptive_sensitivity + 0.05)
        # Decrease sensitivity if accuracy is low
        elif self.performance.accuracy_score < 0.6:
            self.adaptive_sensitivity = max(0.5, self.adaptive_sensitivity - 0.05)

    def execute_gesture_action(self, gesture: str, confidence: float):
        """Execute game action based on recognized gesture"""
        profile = self.game_profiles[self.current_profile]
        
        # Apply adaptive sensitivity
        adjusted_confidence = confidence * self.adaptive_sensitivity
        
        if gesture in profile.gestures and adjusted_confidence > self.gesture_confidence_threshold:
            action = profile.gestures[gesture]
            pyautogui.press(action)
            
            print(f"🎮 {gesture} ({confidence:.2f}) → {action}")
            
            # Update performance stats
            self.update_performance_stats(gesture, confidence)
            
            return True
        
        return False

    def render_advanced_ui(self, frame: np.ndarray, current_gesture: str, confidence: float) -> np.ndarray:
        """Render advanced UI with performance metrics"""
        height, width = frame.shape[:2]
        
        # Main gesture display
        gesture_color = (0, 255, 0) if confidence > 0.8 else (0, 255, 255) if confidence > 0.5 else (0, 0, 255)
        cv2.putText(frame, f"Gesture: {current_gesture}", (10, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, gesture_color, 3)
        
        cv2.putText(frame, f"Confidence: {confidence:.2f}", (10, 80), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, gesture_color, 2)
        
        # Performance metrics panel
        metrics_y = 120
        cv2.putText(frame, "PERFORMANCE STATS", (10, metrics_y), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        stats_text = [
            f"Gestures: {self.performance.gestures_performed}",
            f"Accuracy: {self.performance.accuracy_score:.2f}",
            f"Best Streak: {self.performance.best_streak}",
            f"Avg Reaction: {self.performance.avg_reaction_time:.3f}s",
            f"Session: {self.performance.session_time:.1f}s"
        ]
        
        for i, text in enumerate(stats_text):
            cv2.putText(frame, text, (10, metrics_y + 30 + i * 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 1)
        
        # Game profile info
        profile = self.game_profiles[self.current_profile]
        cv2.putText(frame, f"Profile: {profile.name}", (10, height - 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        cv2.putText(frame, f"Sensitivity: {self.adaptive_sensitivity:.2f}", (10, height - 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 1)
        
        # Gesture guide
        guide_x = width - 350
        cv2.putText(frame, "GESTURE GUIDE", (guide_x, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        gesture_guide = [
            "✋ Open Palm → Jump",
            "✊ Fist → Slide", 
            "👉 Point → Turn",
            "✌️ Peace → Pause",
            "👍 Thumbs Up → Boost",
            "🤘 Rock Sign → Special"
        ]
        
        for i, text in enumerate(gesture_guide):
            cv2.putText(frame, text, (guide_x, 60 + i * 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Cooldown indicator
        if self.smart_cooldown.is_active():
            remaining = self.smart_cooldown.get_remaining_frames()
            cv2.rectangle(frame, (width - 100, height - 50), (width - 10, height - 20), (0, 0, 255), -1)
            cv2.putText(frame, f"CD: {remaining}", (width - 90, height - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return frame

    def run(self):
        """Main execution loop"""
        print("🚀 Starting Smart Gesture Controller...")
        print(f"🎮 Current Profile: {self.game_profiles[self.current_profile].name}")
        print("📊 Advanced features: AI recognition, performance tracking, adaptive controls")
        print("🎯 Press 'q' to quit, 'p' to change profile")
        
        frame_count = 0
        
        while True:
            success, frame = self.cap.read()
            if not success:
                continue
            
            frame = cv2.flip(frame, 1)
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Process hand tracking
            hand_results = self.hands.process(frame_rgb)
            current_gesture = "none"
            confidence = 0.0
            
            if hand_results.multi_hand_landmarks:
                for hand_landmarks in hand_results.multi_hand_landmarks:
                    # Draw hand landmarks
                    self.mp_draw.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS)
                    
                    # Advanced gesture recognition
                    current_gesture, confidence = self.advanced_gesture_recognition(hand_landmarks)
                    
                    # Update position history
                    index_tip = hand_landmarks.landmark[8]
                    self.position_history.append((index_tip.x, index_tip.y, index_tip.z))
                    
                    # Execute gesture action with smart cooldown
                    frame_count += 1
                    if frame_count % 2 == 0 and not self.smart_cooldown.is_active():
                        if self.execute_gesture_action(current_gesture, confidence):
                            profile = self.game_profiles[self.current_profile]
                            self.smart_cooldown.activate(profile.cooldown_frames)
            
            # Update cooldown
            self.smart_cooldown.update()
            
            # Adapt sensitivity based on performance
            if frame_count % 60 == 0:  # Every second at 60 FPS
                self.adapt_sensitivity()
            
            # Render advanced UI
            frame = self.render_advanced_ui(frame, current_gesture, confidence)
            
            # Display
            cv2.imshow("🚀 Smart Gesture Controller", frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('p'):
                self.switch_profile()
        
        self.cleanup()

    def switch_profile(self):
        """Switch between game profiles"""
        profiles = list(self.game_profiles.keys())
        current_index = profiles.index(self.current_profile)
        next_index = (current_index + 1) % len(profiles)
        self.current_profile = profiles[next_index]
        
        profile = self.game_profiles[self.current_profile]
        print(f"🔄 Switched to profile: {profile.name}")

    def cleanup(self):
        """Clean up resources"""
        self.cap.release()
        cv2.destroyAllWindows()
        
        # Print final performance report
        print("\n📊 FINAL PERFORMANCE REPORT")
        print("=" * 40)
        print(f"Total Gestures: {self.performance.gestures_performed}")
        print(f"Accuracy Score: {self.performance.accuracy_score:.2f}")
        print(f"Best Streak: {self.performance.best_streak}")
        print(f"Average Reaction Time: {self.performance.avg_reaction_time:.3f}s")
        print(f"Session Duration: {self.performance.session_time:.1f}s")
        print("🛑 Smart Gesture Controller stopped")


class SmartCooldownManager:
    """Intelligent cooldown management system"""
    
    def __init__(self):
        self.cooldown_frames = 0
        self.adaptive_cooldown = True
        self.performance_history = deque(maxlen=10)
    
    def activate(self, frames: int):
        """Activate cooldown with adaptive adjustment"""
        if self.adaptive_cooldown and len(self.performance_history) > 5:
            avg_performance = sum(self.performance_history) / len(self.performance_history)
            if avg_performance > 0.8:
                frames = max(1, int(frames * 0.7))  # Reduce cooldown for good performance
            elif avg_performance < 0.5:
                frames = int(frames * 1.3)  # Increase cooldown for poor performance
        
        self.cooldown_frames = frames
    
    def update(self):
        """Update cooldown state"""
        if self.cooldown_frames > 0:
            self.cooldown_frames -= 1
    
    def is_active(self) -> bool:
        """Check if cooldown is active"""
        return self.cooldown_frames > 0
    
    def get_remaining_frames(self) -> int:
        """Get remaining cooldown frames"""
        return self.cooldown_frames
    
    def add_performance_sample(self, performance: float):
        """Add performance sample for adaptive cooldown"""
        self.performance_history.append(performance)


if __name__ == "__main__":
    try:
        controller = SmartGestureController()
        controller.run()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down Smart Gesture Controller...")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
