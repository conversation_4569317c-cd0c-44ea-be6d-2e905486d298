#!/usr/bin/env python3
"""
🎮 Game Connector - Universal Game Integration System
Solves network issues and provides multiple connection methods
"""

import socket
import threading
import time
import json
import subprocess
import sys
import os
from typing import Dict, List, Optional, Callable
import pyautogui
import keyboard
import win32gui
import win32con
import win32api

class GameConnector:
    """Universal game integration system with multiple connection methods"""
    
    def __init__(self):
        self.connection_methods = {
            'keyboard': KeyboardConnector(),
            'websocket': WebSocketConnector(),
            'named_pipe': NamedPipeConnector(),
            'tcp_socket': TCPSocketConnector(),
            'window_focus': WindowFocusConnector()
        }
        
        self.active_connections = []
        self.gesture_queue = []
        self.is_running = False
        
        # Game detection
        self.supported_games = {
            'temple_run': {
                'window_titles': ['Temple Run', 'TempleRun', 'temple run'],
                'process_names': ['TempleRun.exe', 'temple_run.exe'],
                'gestures': {
                    'open_palm': 'up',
                    'fist': 'down',
                    'point_left': 'left',
                    'point_right': 'right',
                    'peace_sign': 'space'
                }
            },
            'subway_surfers': {
                'window_titles': ['Subway Surfers', 'SubwaySurfers'],
                'process_names': ['SubwaySurfers.exe', 'subway_surfers.exe'],
                'gestures': {
                    'open_palm': 'up',
                    'fist': 'down',
                    'point_left': 'left',
                    'point_right': 'right'
                }
            },
            'generic': {
                'window_titles': ['*'],
                'process_names': ['*'],
                'gestures': {
                    'open_palm': 'w',
                    'fist': 's',
                    'point_left': 'a',
                    'point_right': 'd',
                    'peace_sign': 'space'
                }
            }
        }
        
        print("🎮 Game Connector initialized with multiple connection methods")

    def detect_games(self) -> List[Dict]:
        """Detect running games"""
        detected_games = []
        
        # Get all windows
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title:
                    windows.append({
                        'hwnd': hwnd,
                        'title': window_title,
                        'process_id': win32gui.GetWindowThreadProcessId(hwnd)[1]
                    })
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        # Match against supported games
        for window in windows:
            for game_name, game_info in self.supported_games.items():
                for title_pattern in game_info['window_titles']:
                    if title_pattern == '*' or title_pattern.lower() in window['title'].lower():
                        detected_games.append({
                            'game': game_name,
                            'window': window,
                            'config': game_info
                        })
                        break
        
        return detected_games

    def setup_connections(self, methods: List[str] = None) -> bool:
        """Setup multiple connection methods"""
        if methods is None:
            methods = ['keyboard', 'window_focus']  # Default safe methods
        
        success_count = 0
        
        for method in methods:
            if method in self.connection_methods:
                try:
                    connector = self.connection_methods[method]
                    if connector.initialize():
                        self.active_connections.append(connector)
                        success_count += 1
                        print(f"✅ {method} connection established")
                    else:
                        print(f"⚠️  {method} connection failed")
                except Exception as e:
                    print(f"❌ {method} connection error: {e}")
        
        if success_count > 0:
            print(f"🎮 {success_count}/{len(methods)} connections active")
            return True
        else:
            print("❌ No connections established")
            return False

    def send_gesture(self, gesture: str, confidence: float = 1.0):
        """Send gesture to all active connections"""
        if confidence < 0.7:  # Only send high-confidence gestures
            return
        
        gesture_data = {
            'gesture': gesture,
            'confidence': confidence,
            'timestamp': time.time()
        }
        
        # Add to queue for processing
        self.gesture_queue.append(gesture_data)
        
        # Send to all active connections
        for connector in self.active_connections:
            try:
                connector.send_gesture(gesture_data)
            except Exception as e:
                print(f"⚠️  Connection error: {e}")

    def start_processing(self):
        """Start gesture processing thread"""
        self.is_running = True
        processing_thread = threading.Thread(target=self._process_gestures, daemon=True)
        processing_thread.start()
        print("🚀 Gesture processing started")

    def stop_processing(self):
        """Stop gesture processing"""
        self.is_running = False
        for connector in self.active_connections:
            connector.cleanup()
        print("🛑 Gesture processing stopped")

    def _process_gestures(self):
        """Process gesture queue"""
        while self.is_running:
            if self.gesture_queue:
                gesture_data = self.gesture_queue.pop(0)
                self._execute_gesture(gesture_data)
            time.sleep(0.01)  # 100 FPS processing

    def _execute_gesture(self, gesture_data: Dict):
        """Execute gesture action"""
        gesture = gesture_data['gesture']
        
        # Detect active game
        detected_games = self.detect_games()
        
        if detected_games:
            game_config = detected_games[0]['config']  # Use first detected game
            if gesture in game_config['gestures']:
                action = game_config['gestures'][gesture]
                print(f"🎮 {gesture} → {action}")
                
                # Execute action through all connectors
                for connector in self.active_connections:
                    connector.execute_action(action, detected_games[0])


class BaseConnector:
    """Base class for game connectors"""
    
    def initialize(self) -> bool:
        """Initialize the connector"""
        return True
    
    def send_gesture(self, gesture_data: Dict):
        """Send gesture data"""
        pass
    
    def execute_action(self, action: str, game_info: Dict):
        """Execute game action"""
        pass
    
    def cleanup(self):
        """Cleanup resources"""
        pass


class KeyboardConnector(BaseConnector):
    """Direct keyboard input connector"""
    
    def __init__(self):
        self.last_action_time = 0
        self.cooldown = 0.2  # 200ms cooldown
    
    def initialize(self) -> bool:
        try:
            # Test keyboard access
            pyautogui.press('ctrl')  # Safe test key
            return True
        except Exception:
            return False
    
    def execute_action(self, action: str, game_info: Dict):
        """Execute keyboard action"""
        current_time = time.time()
        if current_time - self.last_action_time < self.cooldown:
            return
        
        try:
            # Focus game window first
            if 'window' in game_info:
                win32gui.SetForegroundWindow(game_info['window']['hwnd'])
                time.sleep(0.05)  # Brief delay for window focus
            
            # Send key press
            pyautogui.press(action)
            self.last_action_time = current_time
            
        except Exception as e:
            print(f"⚠️  Keyboard action failed: {e}")


class WindowFocusConnector(BaseConnector):
    """Window-focused input connector"""
    
    def __init__(self):
        self.target_window = None
    
    def initialize(self) -> bool:
        return True
    
    def execute_action(self, action: str, game_info: Dict):
        """Execute action with window focus"""
        try:
            if 'window' in game_info:
                hwnd = game_info['window']['hwnd']
                
                # Ensure window is active
                if win32gui.GetForegroundWindow() != hwnd:
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(0.05)
                
                # Send key to specific window
                self._send_key_to_window(hwnd, action)
                
        except Exception as e:
            print(f"⚠️  Window focus action failed: {e}")
    
    def _send_key_to_window(self, hwnd: int, key: str):
        """Send key directly to window"""
        key_codes = {
            'up': win32con.VK_UP,
            'down': win32con.VK_DOWN,
            'left': win32con.VK_LEFT,
            'right': win32con.VK_RIGHT,
            'space': win32con.VK_SPACE,
            'w': ord('W'),
            's': ord('S'),
            'a': ord('A'),
            'd': ord('D')
        }
        
        if key in key_codes:
            vk_code = key_codes[key]
            # Send key down and up
            win32api.PostMessage(hwnd, win32con.WM_KEYDOWN, vk_code, 0)
            time.sleep(0.05)
            win32api.PostMessage(hwnd, win32con.WM_KEYUP, vk_code, 0)


class WebSocketConnector(BaseConnector):
    """WebSocket connector for web-based games"""
    
    def __init__(self):
        self.server = None
        self.clients = []
        self.port = 8765
    
    def initialize(self) -> bool:
        try:
            import websockets
            import asyncio
            
            # Start WebSocket server
            self.server = websockets.serve(self.handle_client, "localhost", self.port)
            print(f"🌐 WebSocket server starting on port {self.port}")
            return True
            
        except ImportError:
            print("⚠️  WebSocket support requires 'websockets' package")
            return False
        except Exception as e:
            print(f"⚠️  WebSocket server failed: {e}")
            return False
    
    async def handle_client(self, websocket, path):
        """Handle WebSocket client connections"""
        self.clients.append(websocket)
        try:
            await websocket.wait_closed()
        finally:
            self.clients.remove(websocket)
    
    def send_gesture(self, gesture_data: Dict):
        """Send gesture to WebSocket clients"""
        if self.clients:
            message = json.dumps(gesture_data)
            for client in self.clients:
                try:
                    # Send to client (would need async handling in real implementation)
                    pass
                except Exception:
                    pass


class TCPSocketConnector(BaseConnector):
    """TCP Socket connector for network games"""
    
    def __init__(self):
        self.server_socket = None
        self.clients = []
        self.port = 9999
    
    def initialize(self) -> bool:
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', self.port))
            self.server_socket.listen(5)
            
            # Start accepting connections
            accept_thread = threading.Thread(target=self._accept_connections, daemon=True)
            accept_thread.start()
            
            print(f"🔌 TCP server listening on port {self.port}")
            return True
            
        except Exception as e:
            print(f"⚠️  TCP server failed: {e}")
            return False
    
    def _accept_connections(self):
        """Accept TCP client connections"""
        while True:
            try:
                client_socket, address = self.server_socket.accept()
                self.clients.append(client_socket)
                print(f"🔌 Client connected from {address}")
                
                # Handle client in separate thread
                client_thread = threading.Thread(
                    target=self._handle_client, 
                    args=(client_socket,), 
                    daemon=True
                )
                client_thread.start()
                
            except Exception as e:
                print(f"⚠️  Accept connection error: {e}")
                break
    
    def _handle_client(self, client_socket):
        """Handle individual TCP client"""
        try:
            while True:
                data = client_socket.recv(1024)
                if not data:
                    break
                # Echo back for testing
                client_socket.send(b"ACK")
        except Exception:
            pass
        finally:
            if client_socket in self.clients:
                self.clients.remove(client_socket)
            client_socket.close()
    
    def send_gesture(self, gesture_data: Dict):
        """Send gesture to TCP clients"""
        message = json.dumps(gesture_data).encode('utf-8')
        for client in self.clients[:]:  # Copy list to avoid modification during iteration
            try:
                client.send(message)
            except Exception:
                self.clients.remove(client)


class NamedPipeConnector(BaseConnector):
    """Named pipe connector for local applications"""
    
    def __init__(self):
        self.pipe_name = r'\\.\pipe\gesture_controller'
        self.pipe = None
    
    def initialize(self) -> bool:
        try:
            import win32pipe
            import win32file
            
            # Create named pipe
            self.pipe = win32pipe.CreateNamedPipe(
                self.pipe_name,
                win32pipe.PIPE_ACCESS_DUPLEX,
                win32pipe.PIPE_TYPE_MESSAGE | win32pipe.PIPE_READMODE_MESSAGE | win32pipe.PIPE_WAIT,
                1, 65536, 65536, 0, None
            )
            
            print(f"📡 Named pipe created: {self.pipe_name}")
            return True
            
        except ImportError:
            print("⚠️  Named pipe requires pywin32 package")
            return False
        except Exception as e:
            print(f"⚠️  Named pipe failed: {e}")
            return False
    
    def send_gesture(self, gesture_data: Dict):
        """Send gesture through named pipe"""
        if self.pipe:
            try:
                message = json.dumps(gesture_data).encode('utf-8')
                # Would implement pipe communication here
            except Exception as e:
                print(f"⚠️  Named pipe send failed: {e}")


def main():
    """Test the game connector"""
    print("🎮 Testing Game Connector System")
    print("=" * 50)
    
    connector = GameConnector()
    
    # Detect games
    games = connector.detect_games()
    print(f"🔍 Detected {len(games)} games:")
    for game in games:
        print(f"   • {game['game']}: {game['window']['title']}")
    
    # Setup connections
    if connector.setup_connections(['keyboard', 'window_focus']):
        connector.start_processing()
        
        # Test gestures
        print("\n🧪 Testing gestures...")
        test_gestures = ['open_palm', 'fist', 'point_left', 'point_right']
        
        for gesture in test_gestures:
            print(f"Testing {gesture}...")
            connector.send_gesture(gesture, 0.9)
            time.sleep(1)
        
        connector.stop_processing()
    
    print("✅ Game connector test complete")


if __name__ == "__main__":
    main()
