/**
 * 🚀 AI Gesture Gaming Controller - Web Version
 * Revolutionary browser-based gesture recognition system
 */

class AIGestureController {
    constructor() {
        this.hands = null;
        this.camera = null;
        this.videoElement = document.getElementById('videoElement');
        this.canvasElement = document.getElementById('canvasElement');
        this.canvasCtx = this.canvasElement.getContext('2d');
        
        // Performance tracking
        this.stats = {
            gesturesDetected: 0,
            accuracy: 0,
            bestStreak: 0,
            currentStreak: 0,
            sessionStart: Date.now(),
            confidenceHistory: []
        };
        
        // Gesture history for smoothing
        this.gestureHistory = [];
        this.maxHistoryLength = 10;
        
        // Current state
        this.currentGesture = 'none';
        this.currentConfidence = 0;
        this.isActive = false;
        
        // Game profiles
        this.gameProfiles = {
            temple_run: {
                name: 'Temple Run',
                gestures: {
                    'open_palm': '↑ Jump',
                    'fist': '↓ Slide',
                    'point_left': '← Turn Left',
                    'point_right': '→ Turn Right',
                    'peace_sign': '⏸️ Pause',
                    'thumbs_up': '⚡ Boost'
                }
            },
            subway_surfers: {
                name: 'Subway Surfers',
                gestures: {
                    'open_palm': '↑ Jump',
                    'fist': '↓ Roll',
                    'point_left': '← Move Left',
                    'point_right': '→ Move Right',
                    'two_fingers': '🛹 Hoverboard'
                }
            },
            custom: {
                name: 'Custom Game',
                gestures: {
                    'open_palm': 'W Key',
                    'fist': 'S Key',
                    'point_left': 'A Key',
                    'point_right': 'D Key',
                    'peace_sign': 'Space'
                }
            }
        };
        
        this.currentProfile = 'temple_run';
        
        this.init();
    }
    
    async init() {
        try {
            await this.initializeMediaPipe();
            this.setupEventListeners();
            this.populateGestureGuide();
            this.startStatsTimer();
            this.updateStatus('🟡 Ready - Click Start Camera');
        } catch (error) {
            console.error('Initialization error:', error);
            this.showError('Failed to initialize gesture recognition system');
        }
    }
    
    async initializeMediaPipe() {
        this.hands = new Hands({
            locateFile: (file) => {
                return `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`;
            }
        });
        
        this.hands.setOptions({
            maxNumHands: 1,
            modelComplexity: 1,
            minDetectionConfidence: 0.7,
            minTrackingConfidence: 0.5
        });
        
        this.hands.onResults(this.onResults.bind(this));
    }
    
    setupEventListeners() {
        // Control buttons
        document.getElementById('startBtn').addEventListener('click', () => this.startCamera());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopCamera());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetStats());
        document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());
        
        // Profile buttons
        document.querySelectorAll('.profile-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchProfile(e.target.dataset.profile));
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case ' ':
                    e.preventDefault();
                    this.isActive ? this.stopCamera() : this.startCamera();
                    break;
                case 'r':
                    this.resetStats();
                    break;
                case 'f':
                    this.toggleFullscreen();
                    break;
            }
        });
    }
    
    async startCamera() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: 1280,
                    height: 720,
                    facingMode: 'user'
                }
            });
            
            this.videoElement.srcObject = stream;
            
            this.camera = new Camera(this.videoElement, {
                onFrame: async () => {
                    await this.hands.send({image: this.videoElement});
                },
                width: 1280,
                height: 720
            });
            
            await this.camera.start();
            
            this.isActive = true;
            this.updateStatus('🟢 Active - Gesture Recognition Running');
            
            document.getElementById('startBtn').classList.add('active');
            document.getElementById('stopBtn').classList.remove('active');
            
        } catch (error) {
            console.error('Camera error:', error);
            this.showError('Camera access denied or not available');
        }
    }
    
    stopCamera() {
        if (this.camera) {
            this.camera.stop();
        }
        
        if (this.videoElement.srcObject) {
            this.videoElement.srcObject.getTracks().forEach(track => track.stop());
            this.videoElement.srcObject = null;
        }
        
        this.isActive = false;
        this.updateStatus('🔴 Stopped');
        
        document.getElementById('startBtn').classList.remove('active');
        document.getElementById('stopBtn').classList.add('active');
        
        // Clear canvas
        this.canvasCtx.clearRect(0, 0, this.canvasElement.width, this.canvasElement.height);
    }
    
    onResults(results) {
        // Resize canvas to match video
        this.canvasElement.width = this.videoElement.videoWidth;
        this.canvasElement.height = this.videoElement.videoHeight;
        
        // Clear canvas
        this.canvasCtx.clearRect(0, 0, this.canvasElement.width, this.canvasElement.height);
        
        // Draw hand landmarks
        if (results.multiHandLandmarks && results.multiHandLandmarks.length > 0) {
            for (const landmarks of results.multiHandLandmarks) {
                this.drawHandLandmarks(landmarks);
                
                // Recognize gesture
                const [gesture, confidence] = this.recognizeGesture(landmarks);
                this.updateGestureDisplay(gesture, confidence);
                
                // Update performance stats
                this.updatePerformanceStats(gesture, confidence);
            }
        } else {
            this.updateGestureDisplay('none', 0);
        }
    }
    
    drawHandLandmarks(landmarks) {
        // Draw connections
        const connections = [
            [0, 1], [1, 2], [2, 3], [3, 4], // Thumb
            [0, 5], [5, 6], [6, 7], [7, 8], // Index
            [0, 9], [9, 10], [10, 11], [11, 12], // Middle
            [0, 13], [13, 14], [14, 15], [15, 16], // Ring
            [0, 17], [17, 18], [18, 19], [19, 20], // Pinky
            [5, 9], [9, 13], [13, 17] // Palm
        ];
        
        this.canvasCtx.strokeStyle = '#00ff00';
        this.canvasCtx.lineWidth = 2;
        
        connections.forEach(([start, end]) => {
            const startPoint = landmarks[start];
            const endPoint = landmarks[end];
            
            this.canvasCtx.beginPath();
            this.canvasCtx.moveTo(
                startPoint.x * this.canvasElement.width,
                startPoint.y * this.canvasElement.height
            );
            this.canvasCtx.lineTo(
                endPoint.x * this.canvasElement.width,
                endPoint.y * this.canvasElement.height
            );
            this.canvasCtx.stroke();
        });
        
        // Draw landmarks
        this.canvasCtx.fillStyle = '#ff0000';
        landmarks.forEach(landmark => {
            this.canvasCtx.beginPath();
            this.canvasCtx.arc(
                landmark.x * this.canvasElement.width,
                landmark.y * this.canvasElement.height,
                5, 0, 2 * Math.PI
            );
            this.canvasCtx.fill();
        });
    }
    
    recognizeGesture(landmarks) {
        // Analyze finger states
        const fingersUp = this.getFingerStates(landmarks);
        const fingerCount = fingersUp.filter(Boolean).length;
        
        // Calculate hand metrics
        const handMetrics = this.calculateHandMetrics(landmarks);
        
        // Gesture classification
        let gesture = 'unknown';
        let confidence = 0.3;
        
        if (fingerCount === 0) {
            gesture = 'fist';
            confidence = 0.9;
        } else if (fingerCount === 5) {
            gesture = 'open_palm';
            confidence = 0.9;
        } else if (fingersUp[1] && !fingersUp[2] && !fingersUp[3] && !fingersUp[4]) {
            // Index finger only
            if (handMetrics.orientation > 45) {
                gesture = 'point_left';
            } else if (handMetrics.orientation < -45) {
                gesture = 'point_right';
            } else {
                gesture = 'point';
            }
            confidence = 0.85;
        } else if (fingersUp[1] && fingersUp[2] && !fingersUp[3] && !fingersUp[4]) {
            gesture = 'peace_sign';
            confidence = 0.9;
        } else if (fingersUp[0] && !fingersUp[1] && !fingersUp[2] && !fingersUp[3] && !fingersUp[4]) {
            gesture = 'thumbs_up';
            confidence = 0.8;
        } else if (fingersUp[1] && fingersUp[2] && !fingersUp[3] && !fingersUp[4]) {
            gesture = 'two_fingers';
            confidence = 0.75;
        }
        
        // Apply temporal smoothing
        return this.applyTemporalSmoothing(gesture, confidence);
    }
    
    getFingerStates(landmarks) {
        const fingers = [];
        
        // Thumb (check x-axis)
        fingers.push(Math.abs(landmarks[4].x - landmarks[3].x) > 0.04);
        
        // Other fingers (check y-axis)
        const fingerTips = [8, 12, 16, 20];
        const fingerPips = [6, 10, 14, 18];
        
        for (let i = 0; i < fingerTips.length; i++) {
            fingers.push(landmarks[fingerTips[i]].y < landmarks[fingerPips[i]].y - 0.02);
        }
        
        return fingers;
    }
    
    calculateHandMetrics(landmarks) {
        // Hand orientation
        const wrist = landmarks[0];
        const middleMcp = landmarks[9];
        const orientation = Math.atan2(middleMcp.y - wrist.y, middleMcp.x - wrist.x) * 180 / Math.PI;
        
        // Hand span
        const thumbTip = landmarks[4];
        const pinkyTip = landmarks[20];
        const span = Math.sqrt(
            Math.pow(thumbTip.x - pinkyTip.x, 2) + 
            Math.pow(thumbTip.y - pinkyTip.y, 2)
        );
        
        return { orientation, span };
    }
    
    applyTemporalSmoothing(gesture, confidence) {
        this.gestureHistory.push({ gesture, confidence, timestamp: Date.now() });
        
        if (this.gestureHistory.length > this.maxHistoryLength) {
            this.gestureHistory.shift();
        }
        
        if (this.gestureHistory.length < 3) {
            return [gesture, confidence];
        }
        
        // Find most consistent gesture
        const gestureCounts = {};
        let totalConfidence = 0;
        
        this.gestureHistory.forEach(item => {
            if (!gestureCounts[item.gesture]) {
                gestureCounts[item.gesture] = { count: 0, confidence: 0 };
            }
            gestureCounts[item.gesture].count++;
            gestureCounts[item.gesture].confidence += item.confidence;
            totalConfidence += item.confidence;
        });
        
        let bestGesture = gesture;
        let bestScore = 0;
        
        Object.entries(gestureCounts).forEach(([g, data]) => {
            const consistency = data.count / this.gestureHistory.length;
            const avgConfidence = data.confidence / data.count;
            const score = consistency * avgConfidence;
            
            if (score > bestScore) {
                bestScore = score;
                bestGesture = g;
            }
        });
        
        return [bestGesture, Math.min(bestScore, 1.0)];
    }
    
    updateGestureDisplay(gesture, confidence) {
        this.currentGesture = gesture;
        this.currentConfidence = confidence;
        
        const gestureEmojis = {
            'open_palm': '✋',
            'fist': '✊',
            'point': '👉',
            'point_left': '👈',
            'point_right': '👉',
            'peace_sign': '✌️',
            'thumbs_up': '👍',
            'two_fingers': '✌️',
            'none': '👋',
            'unknown': '❓'
        };
        
        const gestureNames = {
            'open_palm': 'Open Palm',
            'fist': 'Fist',
            'point': 'Pointing',
            'point_left': 'Point Left',
            'point_right': 'Point Right',
            'peace_sign': 'Peace Sign',
            'thumbs_up': 'Thumbs Up',
            'two_fingers': 'Two Fingers',
            'none': 'No Hand Detected',
            'unknown': 'Unknown Gesture'
        };
        
        const emoji = gestureEmojis[gesture] || '❓';
        const name = gestureNames[gesture] || 'Unknown';
        
        document.getElementById('currentGesture').textContent = `${emoji} ${name}`;
        document.getElementById('confidenceFill').style.width = `${confidence * 100}%`;
        document.getElementById('confidenceText').textContent = `Confidence: ${Math.round(confidence * 100)}%`;
        
        // Add visual feedback for high confidence gestures
        const gestureDisplay = document.querySelector('.gesture-display');
        if (confidence > 0.8) {
            gestureDisplay.classList.add('glow');
        } else {
            gestureDisplay.classList.remove('glow');
        }
    }
    
    updatePerformanceStats(gesture, confidence) {
        if (gesture !== 'none' && gesture !== 'unknown' && confidence > 0.6) {
            this.stats.gesturesDetected++;
            this.stats.confidenceHistory.push(confidence);
            
            if (confidence > 0.8) {
                this.stats.currentStreak++;
                this.stats.bestStreak = Math.max(this.stats.bestStreak, this.stats.currentStreak);
            } else {
                this.stats.currentStreak = 0;
            }
            
            // Calculate accuracy
            if (this.stats.confidenceHistory.length > 0) {
                const avgConfidence = this.stats.confidenceHistory.reduce((a, b) => a + b, 0) / this.stats.confidenceHistory.length;
                this.stats.accuracy = Math.round(avgConfidence * 100);
            }
        }
        
        // Update UI
        document.getElementById('gestureCount').textContent = this.stats.gesturesDetected;
        document.getElementById('accuracy').textContent = `${this.stats.accuracy}%`;
        document.getElementById('bestStreak').textContent = this.stats.bestStreak;
        
        const sessionTime = Math.round((Date.now() - this.stats.sessionStart) / 1000);
        document.getElementById('sessionTime').textContent = `${sessionTime}s`;
    }
    
    resetStats() {
        this.stats = {
            gesturesDetected: 0,
            accuracy: 0,
            bestStreak: 0,
            currentStreak: 0,
            sessionStart: Date.now(),
            confidenceHistory: []
        };
        
        this.gestureHistory = [];
        this.updatePerformanceStats('none', 0);
    }
    
    switchProfile(profileName) {
        this.currentProfile = profileName;
        
        // Update UI
        document.querySelectorAll('.profile-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-profile="${profileName}"]`).classList.add('active');
        
        this.populateGestureGuide();
    }
    
    populateGestureGuide() {
        const guide = document.getElementById('gestureGuide');
        const profile = this.gameProfiles[this.currentProfile];
        
        guide.innerHTML = '';
        
        Object.entries(profile.gestures).forEach(([gesture, action]) => {
            const gestureEmojis = {
                'open_palm': '✋',
                'fist': '✊',
                'point_left': '👈',
                'point_right': '👉',
                'peace_sign': '✌️',
                'thumbs_up': '👍',
                'two_fingers': '✌️'
            };
            
            const item = document.createElement('div');
            item.className = 'gesture-item';
            item.innerHTML = `
                <div class="gesture-emoji">${gestureEmojis[gesture] || '❓'}</div>
                <div class="gesture-info">
                    <div class="gesture-name">${gesture.replace('_', ' ').toUpperCase()}</div>
                    <div class="gesture-action">${action}</div>
                </div>
            `;
            guide.appendChild(item);
        });
    }
    
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }
    
    updateStatus(status) {
        document.getElementById('statusIndicator').textContent = status;
        
        if (status.includes('🟢')) {
            document.getElementById('statusIndicator').classList.add('active');
        } else {
            document.getElementById('statusIndicator').classList.remove('active');
        }
    }
    
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error';
        errorDiv.textContent = `❌ ${message}`;
        
        document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.video-section'));
        
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }
    
    startStatsTimer() {
        setInterval(() => {
            if (this.isActive) {
                const sessionTime = Math.round((Date.now() - this.stats.sessionStart) / 1000);
                document.getElementById('sessionTime').textContent = `${sessionTime}s`;
            }
        }, 1000);
    }
}

// Initialize the controller when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new AIGestureController();
});
