@echo off
echo 🚀 AI Gesture Controller Web Server
echo ====================================
echo.
echo Starting the revolutionary web-based gesture controller...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.9+ from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "index.html" (
    echo ❌ index.html not found
    echo Please make sure all files are in the same directory
    pause
    exit /b 1
)

if not exist "gesture-controller.js" (
    echo ❌ gesture-controller.js not found
    echo Please make sure all files are in the same directory
    pause
    exit /b 1
)

echo ✅ All files found
echo 🌐 Starting web server...
echo.

REM Start the web server
python web_server.py

echo.
echo 🛑 Server stopped
pause
