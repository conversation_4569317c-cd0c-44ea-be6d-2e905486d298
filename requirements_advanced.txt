# 🚀 AI Gesture Controller - Advanced Requirements
# Revolutionary gaming control system dependencies

# Core Computer Vision & AI
opencv-python>=4.8.0
mediapipe>=0.10.0
numpy>=1.24.0
scikit-learn>=1.3.0

# Game Control & Automation
pyautogui>=0.9.54
pygame>=2.5.0
pynput>=1.7.6

# Voice Recognition
SpeechRecognition>=3.10.0
pyaudio>=0.2.11
pocketsphinx>=5.0.0

# Machine Learning & Data Science
tensorflow>=2.13.0
torch>=2.0.0
torchvision>=0.15.0
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Advanced Features
dlib>=19.24.0
face-recognition>=1.3.0
imutils>=0.5.4
filterpy>=1.4.5

# Performance & Optimization
numba>=0.57.0
cython>=3.0.0
joblib>=1.3.0

# Data Handling & Storage
h5py>=3.9.0
pickle5>=0.0.12
jsonschema>=4.17.0

# Networking & Communication
websockets>=11.0.0
requests>=2.31.0
flask>=2.3.0

# GUI & Visualization
tkinter-tooltip>=2.0.0
pillow>=10.0.0
plotly>=5.15.0

# System Integration
psutil>=5.9.0
keyboard>=0.13.5
mouse>=0.7.1

# Development & Testing
pytest>=7.4.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# Optional Advanced Features
# Uncomment if you want biometric integration
# heartpy>=1.2.7
# biosppy>=2.0.0

# Uncomment for advanced eye tracking
# tobii-research>=1.10.0
# pupil-labs-realtime-api>=1.0.0

# Uncomment for neural network acceleration
# onnx>=1.14.0
# onnxruntime>=1.15.0
# tensorrt>=8.6.0

# Platform-specific (Windows)
# pywin32>=306
# winsound>=1.0.0

# Platform-specific (Linux)
# python3-xlib>=0.15
# evdev>=1.6.0

# Platform-specific (macOS)
# pyobjc>=9.2
# Quartz>=0.1.0
